import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_cubit.dart';
import 'package:toii_mesh/screen/chats/qr_code_screen.dart';
import 'package:toii_mesh/screen/chats/qr_scanner_screen.dart';

class QrMenuScreen extends StatelessWidget {
  const QrMenuScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'QR Code',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const SizedBox(height: 32),
            _buildMenuOption(
              context,
              icon: Icons.qr_code,
              title: 'View My QR Code',
              subtitle: 'Show your QR code for others to scan',
              onTap: () => _navigateToQrCode(context),
            ),
            const SizedBox(height: 24),
            _buildMenuOption(
              context,
              icon: Icons.qr_code_scanner,
              title: 'Scan QR Code',
              subtitle: 'Scan someone else\'s QR code to connect',
              onTap: () => _navigateToScanner(context),
            ),
            const SizedBox(height: 24),
            _buildMenuOption(
              context,
              icon: Icons.share,
              title: 'Share My QR Code',
              subtitle: 'Share your QR code with others',
              onTap: () => _shareQrCode(context),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFF6F6F6),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Column(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Color(0xFF777777),
                    size: 24,
                  ),
                  SizedBox(height: 8),
                  Text(
                    'QR codes allow you to quickly connect with other Meshii users by sharing your XMTP inbox ID.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF777777),
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: const Color(0xFFE5E5E5)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: const Color(0xFF3C27A6).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: const Color(0xFF3C27A6),
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF777777),
                      height: 1.3,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: Color(0xFF777777),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToQrCode(BuildContext context) {
    final xmtpCubit = context.read<XmtpCubit>();

    if (!xmtpCubit.hasClient) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please initialize XMTP client first'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const QrCodeScreen(),
      ),
    );
  }

  void _navigateToScanner(BuildContext context) {
    final xmtpCubit = context.read<XmtpCubit>();

    if (!xmtpCubit.hasClient) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please initialize XMTP client first'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    Navigator.of(context)
        .push(
      MaterialPageRoute(
        builder: (context) => const QrScannerScreen(),
      ),
    )
        .then((result) {
      if (result == true && context.mounted) {
        // QR scan was successful, pop back to chats
        Navigator.of(context).pop();
      }
    });
  }

  void _shareQrCode(BuildContext context) {
    // Navigate to QR code screen which has share functionality
    _navigateToQrCode(context);
  }
}
