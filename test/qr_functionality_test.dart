import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';

void main() {
  group('QR Code Functionality Tests', () {
    test('QR data format should be valid JSON', () {
      const testInboxId = 'test_inbox_id_123';
      
      final qrData = jsonEncode({
        'type': 'meshii_connect',
        'inboxId': testInboxId,
      });
      
      // Verify the JSON can be decoded
      final decoded = jsonDecode(qrData) as Map<String, dynamic>;
      
      expect(decoded['type'], equals('meshii_connect'));
      expect(decoded['inboxId'], equals(testInboxId));
    });
    
    test('QR data should handle special characters in inbox ID', () {
      const testInboxId = 'test_inbox_id_with-special_chars.123';
      
      final qrData = jsonEncode({
        'type': 'meshii_connect',
        'inboxId': testInboxId,
      });
      
      final decoded = jsonDecode(qrData) as Map<String, dynamic>;
      
      expect(decoded['inboxId'], equals(testInboxId));
    });
    
    test('QR data validation should work correctly', () {
      // Valid QR data
      final validQrData = jsonEncode({
        'type': 'meshii_connect',
        'inboxId': 'valid_inbox_id',
      });
      
      final validDecoded = jsonDecode(validQrData) as Map<String, dynamic>;
      expect(validDecoded['type'], equals('meshii_connect'));
      expect(validDecoded['inboxId'], isNotNull);
      expect(validDecoded['inboxId'], isNotEmpty);
      
      // Invalid QR data (missing type)
      final invalidQrData1 = jsonEncode({
        'inboxId': 'valid_inbox_id',
      });
      
      final invalidDecoded1 = jsonDecode(invalidQrData1) as Map<String, dynamic>;
      expect(invalidDecoded1['type'], isNull);
      
      // Invalid QR data (wrong type)
      final invalidQrData2 = jsonEncode({
        'type': 'other_app_connect',
        'inboxId': 'valid_inbox_id',
      });
      
      final invalidDecoded2 = jsonDecode(invalidQrData2) as Map<String, dynamic>;
      expect(invalidDecoded2['type'], isNot(equals('meshii_connect')));
    });
  });
}
